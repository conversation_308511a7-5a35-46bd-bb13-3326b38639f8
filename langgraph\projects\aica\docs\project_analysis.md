# AICA 智能竞品分析系统 - 项目全面分析文档

## 项目概述

AICA (AI Competitive Analysis) 是一个基于 LangGraph 框架构建的智能竞品分析系统。该系统能够自动化地进行竞品研究、信息收集、分析和报告生成，为企业战略决策提供数据支持。

### 核心特性

1. **智能规划**: 基于用户输入自动识别竞品并制定分析计划
2. **网络搜索**: 集成 DuckDuckGo 搜索引擎进行信息收集
3. **内容抓取**: 自动抓取网页内容并提取关键信息
4. **质量验证**: 内置反思机制确保分析结果质量
5. **报告生成**: 自动生成结构化的竞品分析报告
6. **日志系统**: 完整的彩色日志记录，支持调试和监控

## 项目架构

### 目录结构

```
aica/
├── aica_agent/                 # 核心智能体模块
│   ├── __init__.py
│   ├── chains.py              # LLM 链定义
│   ├── graph.py               # LangGraph 工作流定义
│   ├── nodes.py               # 节点函数实现
│   ├── pdf_builder.py         # PDF 生成器（已屏蔽）
│   ├── state.py               # 状态管理
│   ├── tools/                 # 工具模块
│   │   ├── __init__.py
│   │   └── web_tools.py       # 网络搜索和抓取工具
│   └── utils/                 # 工具模块
│       ├── __init__.py
│       └── logger.py          # 日志系统
├── assets/                    # 资源文件
│   └── TCCEB.TTF             # 中文字体文件
├── docs/                      # 文档目录
├── logs/                      # 日志目录
├── config.py                  # 配置文件
└── main.py                    # 主程序入口
```

### 核心组件

#### 1. 状态管理 (state.py)

- **AgentState**: 定义整个工作流的状态
- **CompetitorAnalysis**: 单个竞品的分析结构
- **SubTask**: 子任务定义

#### 2. 工作流节点 (nodes.py)

- **planning_node**: 规划节点，包含增强的网络搜索功能
- **select_next_competitor_node**: 竞品选择节点
- **select_next_sub_task_node**: 子任务选择节点
- **subtask_execution_node**: 子任务执行节点
- **reflection_node**: 反思验证节点
- **final_report_node**: 最终报告生成节点

#### 3. LLM 链 (chains.py)

- **enhanced_planning_chain**: 增强规划链（包含搜索信息）
- **planning_chain**: 基础规划链
- **subtask_execution_chain**: 子任务执行链
- **validation_chain**: 验证链
- **final_report_chain**: 最终报告生成链

#### 4. 工具系统 (tools/)

- **ddgs_search_tool**: DuckDuckGo 搜索工具
- **scrape_website_tool**: 网页内容抓取工具

#### 5. 日志系统 (utils/logger.py)

- **AICALogger**: 专用日志器
- **ColoredFormatter**: 彩色日志格式化器
- 支持不同阶段的彩色日志输出
- 自动生成带时间戳的日志文件

## 工作流程

### 1. 规划阶段 (Planning Phase)

```mermaid
graph TD
    A[用户输入] --> B[网络搜索]
    B --> C[搜索信息整合]
    C --> D[LLM 分析]
    D --> E[生成分析计划]
    E --> F[识别竞品列表]
    F --> G[定义分析维度]
```

**增强功能**:
- 自动进行网络搜索以了解相关竞品
- 基于搜索结果优化竞品识别
- 生成更准确的分析计划

### 2. 执行阶段 (Execution Phase)

```mermaid
graph TD
    A[选择竞品] --> B[选择子任务]
    B --> C[搜索相关信息]
    C --> D[抓取网页内容]
    D --> E[LLM 分析提取]
    E --> F[生成初步结论]
    F --> G[质量验证]
    G --> H{验证通过?}
    H -->|是| I[保存结果]
    H -->|否| J[重试执行]
    J --> C
    I --> K{还有子任务?}
    K -->|是| B
    K -->|否| L{还有竞品?}
    L -->|是| A
    L -->|否| M[生成最终报告]
```

### 3. 反思阶段 (Reflection Phase)

- 验证分析结果是否满足验收标准
- 支持自动重试机制
- 记录验证过程和结果

### 4. 报告生成阶段 (Reporting Phase)

- 整合所有分析结果
- 生成结构化 Markdown 报告
- 保存到 docs 目录

## 技术栈

### 核心框架
- **LangGraph**: 工作流编排框架
- **LangChain**: LLM 应用开发框架
- **Pydantic**: 数据验证和序列化

### LLM 集成
- **ChatOllama**: 本地 LLM 支持
- **ChatOpenAI**: OpenAI API 支持

### 工具和库
- **DDGS**: DuckDuckGo 搜索
- **WebBaseLoader**: 网页内容加载
- **colorama**: 彩色终端输出
- **ReportLab**: PDF 生成（已屏蔽）

## 配置说明

### 环境变量
- `OPENAI_API_KEY`: OpenAI API 密钥（可选）

### 配置参数
- `MAX_COMPETITORS`: 最大竞品数量 (默认: 5)
- `MAX_SUBTASK_RETRIES`: 子任务最大重试次数 (默认: 3)
- `LLM_MODEL`: 使用的 LLM 模型 (默认: "gpt-4o")

### 本地 LLM 配置
- 基础 URL: `http://192.168.0.92:8080`
- 默认模型: `qwen2.5:32b`

## 日志系统

### 日志级别和颜色
- **DEBUG**: 青色 - 详细调试信息
- **INFO**: 绿色 - 一般信息
- **WARNING**: 黄色 - 警告信息
- **ERROR**: 红色 - 错误信息
- **CRITICAL**: 红底白字 - 严重错误

### 阶段特定日志
- **PLANNING**: 蓝色加粗 - 规划阶段
- **EXECUTION**: 紫色加粗 - 执行阶段
- **REFLECTION**: 黄色加粗 - 反思阶段
- **SEARCH**: 青色加粗 - 搜索阶段
- **SCRAPE**: 白色加粗 - 抓取阶段
- **RESULT**: 绿色加粗 - 结果阶段

### 日志文件
- 自动生成带时间戳的日志文件
- 保存在 `logs/` 目录下
- 格式: `aica_run_YYYYMMDD_HHMMSS.log`

## 输出文件

### 报告文件
- 格式: Markdown
- 位置: `docs/competitive_analysis_report_YYYYMMDD_HHMMSS.md`
- 包含完整的竞品分析结果

### 日志文件
- 格式: 纯文本
- 位置: `logs/aica_run_YYYYMMDD_HHMMSS.log`
- 包含完整的执行过程记录

## 使用方法

### 启动系统
```bash
cd langgraph/projects/aica
python main.py
```

### 输入示例
- "微信小程序"
- "在线教育平台"
- "短视频应用"
- "电商平台"

### 输出示例
系统会自动：
1. 识别相关竞品
2. 制定分析计划
3. 收集和分析信息
4. 生成详细报告
5. 保存结果文件

## 性能特点

### 优势
1. **自动化程度高**: 最小化人工干预
2. **质量保证**: 内置验证和重试机制
3. **可扩展性**: 模块化设计，易于扩展
4. **可观测性**: 完整的日志记录
5. **灵活性**: 支持多种 LLM 后端

### 限制
1. **网络依赖**: 需要稳定的网络连接
2. **LLM 依赖**: 需要可用的 LLM 服务
3. **语言限制**: 主要支持中文分析
4. **搜索限制**: 依赖 DuckDuckGo 搜索结果

## 改进建议

### 短期改进
1. 添加更多搜索引擎支持
2. 优化网页内容提取算法
3. 增加结果缓存机制
4. 添加配置文件验证

### 长期改进
1. 支持多语言分析
2. 集成更多数据源
3. 添加可视化报告
4. 实现分布式执行
5. 添加 Web 界面

## 故障排除

### 常见问题
1. **搜索失败**: 检查网络连接和 DuckDuckGo 可用性
2. **LLM 连接失败**: 检查 LLM 服务状态和配置
3. **编码问题**: 确保系统支持 UTF-8 编码
4. **权限问题**: 检查文件写入权限

### 调试方法
1. 查看彩色日志输出
2. 检查日志文件详细信息
3. 使用 DEBUG 级别日志
4. 检查网络连接状态

## 版本信息

- **当前版本**: 2.0
- **更新日期**: 2025-08-16
- **主要更新**:
  - 增强规划阶段网络搜索功能
  - 完整的彩色日志系统
  - 屏蔽 PDF 功能，改用 Markdown 输出
  - 优化项目结构和文档

---

*本文档由 AICA 系统自动生成和维护*
