from langchain.chat_models import ChatOpenAI
from langchain_core.output_parsers import StrOutputParser

llm = ChatOpenAI(
    model="GuanBao_BianCang_qwen2.5_7b_v0.0.1",  # 本地模型路径 vllm部署后model调用的名字
    api_key="Empty",  # 本地调用不需要 API key
    openai_api_base="http://192.168.0.92:81/v1"
)

from langchain_core.output_parsers import StrOutputParser
chain = (
  llm
  | StrOutputParser()
)
response = chain.invoke("你好，你是谁？")
response

res = chain.stream("阳明腑实证的具体表现有哪些？")
for chunk in res:
    print(chunk, end="",flush=True)