# aica_agent/nodes.py  执行流程
# aica_agent/nodes.py
import json
import os
from datetime import datetime
from .state import AgentState, CompetitorAnalysis, SubTask
from .tools import ddgs_search_tool, scrape_website_tool
from .chains import (
    planning_chain,
    enhanced_planning_chain,
    subtask_execution_chain,
    validation_chain,
    final_report_chain,
)
from config import MAX_SUBTASK_RETRIES
# from .pdf_builder import build_pdf_with_reportlab # 暂时屏蔽PDF功能
from config import REPORTS_DIR, REPORT_FILENAME
from .utils.logger import get_logger

# 节点 1: 感知与规划（增强版）
def planning_node(state: AgentState) -> dict:
    logger = get_logger()
    logger.planning("=" * 60)
    logger.planning("开始增强规划阶段")
    logger.planning("=" * 60)

    logger.planning(f"用户输入: {state.initial_input}")
    logger.planning("第一步：进行网络搜索以了解相关竞品信息...")

    # 第一步：进行网络搜索以了解用户意图和相关竞品
    search_queries = [
        f"{state.initial_input} 竞品分析",
        f"{state.initial_input} 主要竞争对手",
        f"{state.initial_input} 市场分析"
    ]

    search_context = ""
    for query in search_queries:
        logger.search(f"搜索查询: {query}")
        search_results = ddgs_search_tool.invoke({"query": query, "max_results": 3})

        for result in search_results:
            search_context += f"标题: {result['title']}\n"
            search_context += f"摘要: {result['body']}\n"
            search_context += f"链接: {result['href']}\n\n"

    logger.planning(f"搜索上下文长度: {len(search_context)} 字符")
    logger.planning("第二步：基于搜索信息生成详细分析计划...")

    # 记录输入
    logger.log_input_output("PLANNING", {"initial_input": state.initial_input, "search_context_length": len(search_context)}, None)

    # 第二步：使用增强的规划链，结合搜索信息生成计划
    try:
        plan = enhanced_planning_chain.invoke({
            "initial_input": state.initial_input,
            "search_context": search_context
        })
    except Exception as e:
        logger.warning(f"增强规划失败，使用基础规划: {e}")
        plan = planning_chain.invoke({"initial_input": state.initial_input})

    analysis_plan = []
    for competitor_name in plan.competitors_to_research:
        sub_tasks = [
            SubTask(task_name=st.task_name, acceptance_criteria=st.acceptance_criteria)
            for st in plan.sub_tasks
        ]
        analysis_plan.append(
            CompetitorAnalysis(competitor_name=competitor_name, sub_tasks=sub_tasks)
        )

    # 初始化迭代器
    competitor_iterator = iter(analysis_plan)

    # 记录输出
    output_data = {
        "analysis_plan": analysis_plan,
        "competitor_iterator": "迭代器对象"
    }
    logger.log_input_output("PLANNING", None, output_data)

    logger.planning("✅ 增强规划完成，已生成分析计划")
    logger.planning(f"待分析竞品: {[c.competitor_name for c in analysis_plan]}")
    logger.planning(f"分析维度: {[st.task_name for st in analysis_plan[0].sub_tasks]}")

    return {
        "analysis_plan": analysis_plan,
        "competitor_iterator": competitor_iterator
    }

# 节点 2: 初始化下一轮竞品分析
def select_next_competitor_node(state: AgentState) -> dict:
    logger = get_logger()
    logger.execution("=" * 40)
    logger.execution("竞品选择阶段")
    logger.execution("=" * 40)

    try:
        next_competitor = next(state.competitor_iterator)
        logger.execution(f"开始分析下一个竞品: {next_competitor.competitor_name}")
        sub_task_iterator = iter(next_competitor.sub_tasks)

        output_data = {
            "current_competitor_name": next_competitor.competitor_name,
            "sub_task_iterator": "子任务迭代器对象"
        }
        logger.log_input_output("EXECUTION", {"competitor_iterator": "迭代器对象"}, output_data)

        return {
            "current_competitor_name": next_competitor.competitor_name,
            "sub_task_iterator": sub_task_iterator
        }
    except StopIteration:
        logger.execution("所有竞品均已分析完毕")
        return {"current_competitor_name": None} # 发出结束信号

# 节点 3: 初始化下一个子任务
def select_next_sub_task_node(state: AgentState) -> dict:
    logger = get_logger()
    logger.execution("子任务选择阶段")

    try:
        next_sub_task = next(state.sub_task_iterator)
        logger.execution(f"开始子任务: '{next_sub_task.task_name}'")
        logger.execution(f"验收标准: {next_sub_task.acceptance_criteria}")

        output_data = {
            "current_sub_task_name": next_sub_task.task_name,
            "current_sub_task_criteria": next_sub_task.acceptance_criteria,
            "current_retry_count": 0,
            "current_sub_task_result": None
        }
        logger.log_input_output("EXECUTION", {"sub_task_iterator": "迭代器对象"}, output_data)

        return output_data
    except StopIteration:
        logger.execution(f"✅ 对 '{state.current_competitor_name}' 的所有子任务均已完成")
        return {"current_sub_task_name": None} # 发出结束信号

# 节点 4: 行动 - 执行子任务
def subtask_execution_node(state: AgentState) -> dict:
    logger = get_logger()
    competitor = state.current_competitor_name
    sub_task = state.current_sub_task_name

    logger.execution("=" * 30)
    logger.execution(f"执行子任务: {sub_task}")
    logger.execution(f"目标竞品: {competitor}")
    logger.execution("=" * 30)

    # 1. 搜索
    search_query = f"{competitor} {sub_task}"
    logger.search(f"搜索查询: {search_query}")
    search_results = ddgs_search_tool.invoke({"query": search_query, "max_results": 3})
    logger.search(f"搜索结果数量: {len(search_results)}")

    # 2. 抓取
    scraped_content = ""
    for i, res in enumerate(search_results):
        logger.scrape(f"抓取网页 {i+1}/{len(search_results)}: {res['href']}")
        content = scrape_website_tool.invoke(res["href"])
        scraped_content += f"--- URL: {res['href']} ---\n{content}\n\n"
        logger.scrape(f"抓取内容长度: {len(content)} 字符")

    # 3. 提取结论
    logger.execution("正在分析抓取的内容并提取结论...")
    conclusion = subtask_execution_chain.invoke({
        "competitor": competitor,
        "sub_task": sub_task,
        "criteria": state.current_sub_task_criteria,
        "scraped_content": scraped_content
    }).content

    logger.result(f"初步结论已生成，长度: {len(conclusion)} 字符")
    logger.log_input_output("EXECUTION",
                           {"competitor": competitor, "sub_task": sub_task, "criteria": state.current_sub_task_criteria},
                           {"conclusion": conclusion[:200] + "..." if len(conclusion) > 200 else conclusion})

    return {"current_sub_task_result": conclusion}

# 节点 5: 反思 - 验证子任务结果
def reflection_node(state: AgentState) -> dict:
    logger = get_logger()
    logger.reflection("=" * 30)
    logger.reflection(f"验证子任务: {state.current_sub_task_name}")
    logger.reflection("=" * 30)

    logger.reflection("正在验证结论是否满足验收标准...")
    validation = validation_chain.invoke({
        "conclusion": state.current_sub_task_result,
        "criteria": state.current_sub_task_criteria
    })

    if validation.is_sufficient:
        logger.reflection(f"✅ 验证通过: {validation.reasoning}")

        # 将验证通过的结果更新回主计划中
        updated_plan = state.analysis_plan.copy()
        for comp_analysis in updated_plan:
            if comp_analysis.competitor_name == state.current_competitor_name:
                for task in comp_analysis.sub_tasks:
                    if task.task_name == state.current_sub_task_name:
                        task.result = state.current_sub_task_result
                        task.is_complete = True
                        break
                break

        logger.log_input_output("REFLECTION",
                               {"conclusion": state.current_sub_task_result[:100] + "..." if len(state.current_sub_task_result) > 100 else state.current_sub_task_result,
                                "criteria": state.current_sub_task_criteria},
                               {"is_sufficient": True, "reasoning": validation.reasoning})

        return {"analysis_plan": updated_plan}
    else:
        logger.reflection(f"❌ 验证失败: {validation.reasoning}")
        logger.reflection(f"🔄 准备重试 (当前重试次数: {state.current_retry_count + 1})")

        logger.log_input_output("REFLECTION",
                               {"conclusion": state.current_sub_task_result[:100] + "..." if len(state.current_sub_task_result) > 100 else state.current_sub_task_result,
                                "criteria": state.current_sub_task_criteria},
                               {"is_sufficient": False, "reasoning": validation.reasoning})

        return {"current_retry_count": state.current_retry_count + 1}

# 节点 6: 最终报告生成
def final_report_node(state: AgentState) -> dict:
    logger = get_logger()
    logger.result("=" * 60)
    logger.result("开始生成最终报告")
    logger.result("=" * 60)

    logger.result("所有分析已完成，正在整合所有结论生成最终报告...")

    summary = ""
    for comp_analysis in state.analysis_plan:
        summary += f"## 竞品分析: {comp_analysis.competitor_name}\n\n"
        for task in comp_analysis.sub_tasks:
            summary += f"### {task.task_name}\n"
            summary += f"{task.result}\n\n"

    logger.result(f"分析摘要长度: {len(summary)} 字符")
    logger.result("正在生成最终报告...")

    report_markdown = final_report_chain.invoke({"analysis_summary": summary}).content

    # 保存Markdown文档到docs目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    docs_dir = "reports"
    os.makedirs(docs_dir, exist_ok=True)
    markdown_filepath = os.path.join(docs_dir, f"competitive_analysis_report_{timestamp}.md")

    with open(markdown_filepath, 'w', encoding='utf-8') as f:
        f.write(report_markdown)

    logger.result(f"✅ 最终报告已生成: {markdown_filepath}")
    logger.result(f"报告长度: {len(report_markdown)} 字符")

    logger.log_input_output("RESULT",
                           {"analysis_summary": f"摘要长度: {len(summary)} 字符"},
                           {"report_markdown": f"报告长度: {len(report_markdown)} 字符", "filepath": markdown_filepath})

    return {
        "final_report_markdown": report_markdown,
        "report_filepath": markdown_filepath
    }