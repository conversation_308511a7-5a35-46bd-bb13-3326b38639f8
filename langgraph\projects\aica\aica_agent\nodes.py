# aica_agent/nodes.py  执行流程
# aica_agent/nodes.py
import json
from .state import AgentState, CompetitorAnalysis, SubTask
from .tools import ddgs_search_tool, scrape_website_tool
from .chains import (
    planning_chain,
    subtask_execution_chain,
    validation_chain,
    final_report_chain,
)
from config import MAX_SUBTASK_RETRIES
from .pdf_builder import build_pdf_with_reportlab # 我们将PDF生成逻辑移到一个单独的文件中
from config import REPORTS_DIR, REPORT_FILENAME

# 节点 1: 感知与规划
def planning_node(state: AgentState) -> dict:
    print("--- [模块: 规划] ---")
    print("Thought: 理解用户意图，并将其分解为详细的计划...")
    plan = planning_chain.invoke({"initial_input": state.initial_input})
    
    analysis_plan = []
    for competitor_name in plan.competitors_to_research:
        sub_tasks = [
            SubTask(task_name=st.task_name, acceptance_criteria=st.acceptance_criteria)
            for st in plan.sub_tasks
        ]
        analysis_plan.append(
            CompetitorAnalysis(competitor_name=competitor_name, sub_tasks=sub_tasks)
        )
    
    # 初始化迭代器
    competitor_iterator = iter(analysis_plan)
    
    print("✅ 规划完成，已生成分析计划。")
    print(f"   - 待分析竞品: {[c.competitor_name for c in analysis_plan]}")
    print(f"   - 分析维度: {[st.task_name for st in analysis_plan[0].sub_tasks]}")
    
    return {
        "analysis_plan": analysis_plan,
        "competitor_iterator": competitor_iterator
    }

# 节点 2: 初始化下一轮竞品分析
def select_next_competitor_node(state: AgentState) -> dict:
    print("\n--- [模块: 迭代控制] ---")
    try:
        next_competitor = next(state.competitor_iterator)
        print(f"Thought: 开始分析下一个竞品: {next_competitor.competitor_name}")
        sub_task_iterator = iter(next_competitor.sub_tasks)
        return {
            "current_competitor_name": next_competitor.competitor_name,
            "sub_task_iterator": sub_task_iterator
        }
    except StopIteration:
        print("Thought: 所有竞品均已分析完毕。")
        return {"current_competitor_name": None} # 发出结束信号

# 节点 3: 初始化下一个子任务
def select_next_sub_task_node(state: AgentState) -> dict:
    try:
        next_sub_task = next(state.sub_task_iterator)
        print(f"   - 开始子任务: '{next_sub_task.task_name}'")
        return {
            "current_sub_task_name": next_sub_task.task_name,
            "current_sub_task_criteria": next_sub_task.acceptance_criteria,
            "current_retry_count": 0,
            "current_sub_task_result": None
        }
    except StopIteration:
        print(f"✅ 对 '{state.current_competitor_name}' 的所有子任务均已完成。")
        return {"current_sub_task_name": None} # 发出结束信号

# 节点 4: 行动 - 执行子任务
def subtask_execution_node(state: AgentState) -> dict:
    competitor = state.current_competitor_name
    sub_task = state.current_sub_task_name
    print(f"      - [行动] 正在为 '{competitor}' 执行子任务 '{sub_task}'...")
    
    # 1. 搜索
    search_query = f"{competitor} {sub_task}"
    search_results = ddgs_search_tool.invoke({"query": search_query, "max_results": 3})
    
    # 2. 抓取
    scraped_content = ""
    for res in search_results:
        content = scrape_website_tool.invoke(res["href"])
        scraped_content += f"--- URL: {res['href']} ---\n{content}\n\n"
        
    # 3. 提取结论
    conclusion = subtask_execution_chain.invoke({
        "competitor": competitor,
        "sub_task": sub_task,
        "criteria": state.current_sub_task_criteria,
        "scraped_content": scraped_content
    }).content
    
    print(f"      - [观察] 初步结论已生成。")
    return {"current_sub_task_result": conclusion}

# 节点 5: 反思 - 验证子任务结果
def reflection_node(state: AgentState) -> dict:
    print(f"      - [反思] 正在验证子任务 '{state.current_sub_task_name}' 的结论...")
    validation = validation_chain.invoke({
        "conclusion": state.current_sub_task_result,
        "criteria": state.current_sub_task_criteria
    })
    
    if validation.is_sufficient:
        print(f"      - [验证结果] PASS. 理由: {validation.reasoning}")
        
        # 将验证通过的结果更新回主计划中
        updated_plan = state.analysis_plan.copy()
        for comp_analysis in updated_plan:
            if comp_analysis.competitor_name == state.current_competitor_name:
                for task in comp_analysis.sub_tasks:
                    if task.task_name == state.current_sub_task_name:
                        task.result = state.current_sub_task_result
                        task.is_complete = True
                        break
                break
        return {"analysis_plan": updated_plan}
    else:
        print(f"      - [验证结果] FAIL. 理由: {validation.reasoning}")
        return {"current_retry_count": state.current_retry_count + 1}

# 节点 6: 最终报告生成
def final_report_node(state: AgentState) -> dict:
    print("\n--- [模块: 报告生成] ---")
    print("Thought: 所有分析已完成，正在整合所有结论生成最终报告...")
    
    summary = ""
    for comp_analysis in state.analysis_plan:
        summary += f"## 竞品分析: {comp_analysis.competitor_name}\n\n"
        for task in comp_analysis.sub_tasks:
            summary += f"### {task.task_name}\n"
            summary += f"{task.result}\n\n"
    
    report_markdown = final_report_chain.invoke({"analysis_summary": summary}).content
    
    print("   - 正在生成PDF...")
    filepath = build_pdf_with_reportlab(report_markdown)
    print(f"✅ 最终报告已生成: {filepath}")

    return {
        "final_report_markdown": report_markdown,
        "report_filepath": filepath
    }