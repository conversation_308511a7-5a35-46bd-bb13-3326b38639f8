# config.py
import os
from dotenv import load_dotenv

load_dotenv()

# --- LLM and API Keys ---
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
LLM_MODEL = "gpt-4o" # 使用更新、更强的模型

# --- Agent Control ---
MAX_COMPETITORS = 5
MAX_SUBTASK_RETRIES = 3 # 每个子任务的最大重试次数

# --- File Paths ---
REPORTS_DIR = "reports"
REPORT_FILENAME = "competitive_analysis_report.pdf"
CHINESE_FONT_PATH = rf"T:\AIGC\Learn\LLMs\langgraph\projects\aica\assets\TCCEB.TTF"fr