# from langchain_community.utilities import DuckDuckGoSearchAPIWrapper
from ddgs import DDGS
from langchain_community.document_loaders import WebBaseLoader
from langchain_core.tools import tool
from typing import List, Dict 

# 1. 结构化搜索工具
@tool("ddgs_search_tool")
def ddgs_search_tool(query: str, max_results: int = 5) -> List[Dict]:
    """
    使用 DuckDuckGo Search (ddgs) 库执行网络搜索。
    返回一个包含标题、链接和摘要的字典列表。
    """
    print(f"   🔎 正在执行搜索: '{query}'")
    try:
        with DDGS() as ddgs:
            results = list(ddgs.text(query, max_results=max_results,region='zh',language='zh',user_agent='AICA_Agent'))
        # 仅保留我们需要的字段
        return [{"title": r["title"], "href": r["href"], "body": r["body"]} for r in results]
    except Exception as e:
        print(f"   ⚠️ 搜索失败: {e}")
        return []


# 2. 网页抓取工具
@tool("scrape_website_tool")
def scrape_website_tool(url: str) -> str:
    """获取指定URL的文本内容，并进行基本清理。"""
    print(f"   🕸️ 正在抓取URL: {url}")
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    try:
        # WebBaseLoader会自动处理HTML到文本的转换
        loader = WebBaseLoader(
            web_path=url,
            header_template=headers,
            encoding='utf-8'  # 指定编码为UTF-8
        )
        # 只抓取与我们问题相关的文本，避免过长的上下文
        loader.requests_per_second = 2 
        docs = loader.load()
        content = " ".join([d.page_content for d in docs])
        # 限制内容长度，避免LLM上下文溢出
        return content[:15000] 
    except Exception as e:
        print(f"   ⚠️ 抓取失败 {url}: {e}")
        return f"无法从 {url} 加载内容。"