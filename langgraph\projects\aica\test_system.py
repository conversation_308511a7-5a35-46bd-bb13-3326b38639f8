#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AICA 系统测试脚本
用于验证系统各个组件是否正常工作
"""

import os
import sys
from datetime import datetime

def test_imports():
    """测试所有模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from aica_agent.utils.logger import AICALogger, get_logger, init_logger
        print("✅ 日志模块导入成功")
    except Exception as e:
        print(f"❌ 日志模块导入失败: {e}")
        return False
    
    try:
        from aica_agent.tools import ddgs_search_tool, scrape_website_tool
        print("✅ 工具模块导入成功")
    except Exception as e:
        print(f"❌ 工具模块导入失败: {e}")
        return False
    
    try:
        from aica_agent.chains import planning_chain, enhanced_planning_chain
        print("✅ 链模块导入成功")
    except Exception as e:
        print(f"❌ 链模块导入失败: {e}")
        return False
    
    try:
        from aica_agent.graph import build_graph
        print("✅ 图模块导入成功")
    except Exception as e:
        print(f"❌ 图模块导入失败: {e}")
        return False
    
    return True

def test_logger():
    """测试日志系统"""
    print("\n🔍 测试日志系统...")
    
    try:
        from aica_agent.utils.logger import init_logger
        
        # 初始化日志器
        logger = init_logger("test_logs")
        
        # 测试不同类型的日志
        logger.info("这是一条信息日志")
        logger.planning("这是规划阶段日志")
        logger.execution("这是执行阶段日志")
        logger.search("这是搜索阶段日志")
        logger.scrape("这是抓取阶段日志")
        logger.reflection("这是反思阶段日志")
        logger.result("这是结果阶段日志")
        logger.warning("这是警告日志")
        logger.error("这是错误日志")
        
        print("✅ 日志系统测试成功")
        return True
    except Exception as e:
        print(f"❌ 日志系统测试失败: {e}")
        return False

def test_search_tool():
    """测试搜索工具"""
    print("\n🔍 测试搜索工具...")
    
    try:
        from aica_agent.tools import ddgs_search_tool
        from aica_agent.utils.logger import get_logger
        
        # 执行一个简单的搜索
        results = ddgs_search_tool.invoke({"query": "人工智能", "max_results": 2})
        
        if results and len(results) > 0:
            print(f"✅ 搜索工具测试成功，获得 {len(results)} 个结果")
            for i, result in enumerate(results):
                print(f"   结果 {i+1}: {result['title'][:50]}...")
            return True
        else:
            print("❌ 搜索工具测试失败：未获得搜索结果")
            return False
    except Exception as e:
        print(f"❌ 搜索工具测试失败: {e}")
        return False

def test_scrape_tool():
    """测试网页抓取工具"""
    print("\n🔍 测试网页抓取工具...")
    
    try:
        from aica_agent.tools import scrape_website_tool
        
        # 抓取一个简单的网页
        content = scrape_website_tool.invoke("https://www.baidu.com")
        
        if content and len(content) > 0:
            print(f"✅ 网页抓取工具测试成功，获得 {len(content)} 字符")
            return True
        else:
            print("❌ 网页抓取工具测试失败：未获得内容")
            return False
    except Exception as e:
        print(f"❌ 网页抓取工具测试失败: {e}")
        return False

def test_llm_connection():
    """测试LLM连接"""
    print("\n🔍 测试LLM连接...")
    
    try:
        from aica_agent.chains import get_ollama_llm
        
        llm = get_ollama_llm()
        if llm is not None:
            # 测试一个简单的调用
            response = llm.invoke("你好，请回复'测试成功'")
            print(f"✅ LLM连接测试成功，响应: {response.content[:50]}...")
            return True
        else:
            print("❌ LLM连接测试失败：无法创建LLM实例")
            return False
    except Exception as e:
        print(f"❌ LLM连接测试失败: {e}")
        return False

def test_graph_build():
    """测试图构建"""
    print("\n🔍 测试图构建...")
    
    try:
        from aica_agent.graph import build_graph
        
        app = build_graph()
        if app is not None:
            print("✅ 图构建测试成功")
            return True
        else:
            print("❌ 图构建测试失败：无法构建图")
            return False
    except Exception as e:
        print(f"❌ 图构建测试失败: {e}")
        return False

def test_directories():
    """测试目录结构"""
    print("\n🔍 测试目录结构...")
    
    required_dirs = ["docs", "logs", "aica_agent", "aica_agent/tools", "aica_agent/utils"]
    
    for dir_name in required_dirs:
        if os.path.exists(dir_name):
            print(f"✅ 目录 {dir_name} 存在")
        else:
            print(f"❌ 目录 {dir_name} 不存在")
            return False
    
    return True

def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 AICA 系统测试开始")
    print("=" * 60)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(("模块导入", test_imports()))
    test_results.append(("目录结构", test_directories()))
    test_results.append(("日志系统", test_logger()))
    test_results.append(("搜索工具", test_search_tool()))
    test_results.append(("抓取工具", test_scrape_tool()))
    test_results.append(("LLM连接", test_llm_connection()))
    test_results.append(("图构建", test_graph_build()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:12} : {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪。")
        return True
    else:
        print("⚠️  部分测试失败，请检查系统配置。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
