# aica_agent/chains.py Agent "大脑"的核心，负责思考、规划、验证和总结。
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from langchain_ollama import ChatOllama
from typing import List
# from .state import AnalysisScope, CompetitorProfile
from pydantic import BaseModel, Field
from .state import CompetitorAnalysis, SubTask
# 初始化LLM（单例模式，在整个应用中共享）
def get_ollama_llm(model_name="qwen2.5:32b"):
    base_url="http://************:8080"
    try:
        return ChatOllama(
            base_url=base_url, 
            model=model_name)
    except Exception as e:
        print(f"Error: {e}\ntry run ollama list to get model_name")
        return None
# llm = get_ollama_llm()
llm = ChatOpenAI(
      model="GuanBao_BianCang_qwen2.5_7b_v0.0.1",
      api_key="Empty",
      openai_api_base="http://************:81/v1"
  )

# --- 定义所有LLM链 ---

# --- 1. 意图推理 & 任务拆解 (规划) ---
class SubTaskDecomposition(BaseModel):
    task_name: str = Field(description="子任务的名称, 例如 '核心功能与产品定位'")
    acceptance_criteria: str = Field(description="用于验证该子任务是否完成的验收标准")

class Plan(BaseModel):
    competitors_to_research: List[str] = Field(description="基于用户输入识别出的具体竞品名称列表")
    sub_tasks: List[SubTaskDecomposition] = Field(description="为分析每个竞品而拆解出的通用子任务列表及其验收标准")

# 增强的规划提示词，包含搜索信息
ENHANCED_PLANNING_PROMPT = """你是一个顶级的AI战略分析师。你的任务是理解用户的需求，并将其分解为一个详细、可执行的竞品分析计划。

用户的原始输入是: '{initial_input}'

**网络搜索信息:**
{search_context}

**你的思维过程 (Chain of Thought):**
1.  **识别核心实体**: 从用户输入中提取出核心的参照产品或领域。
2.  **理解用户意图**: 结合网络搜索信息，深入理解用户真正想要分析的内容。
3.  **识别具体竞品**: 基于搜索信息和核心实体，列出最相关、最重要的具体竞品名称（不要使用泛泛的描述）。
4.  **确定分析数量**: 根据领域特点确定合适的竞品分析数量（通常3-5个）。
5.  **设计分析框架**: 作为一个专业的分析师，设计一个通用的分析框架，至少包含4个关键分析维度。
6.  **设定验收标准**: 为每一个分析维度设定一个明确、可量化的验收标准。

**输出格式:**
请严格按照下面的JSON格式输出你的计划。每个子任务必须包含task_name和acceptance_criteria两个字段。

例如：
{{
  "competitors_to_research": ["具体竞品名称A", "具体竞品名称B", "具体竞品名称C"],
  "sub_tasks": [
    {{
      "task_name": "市场占有率分析",
      "acceptance_criteria": "提供每个竞品的市场份额数据和趋势分析"
    }},
    {{
      "task_name": "功能对比分析",
      "acceptance_criteria": "列出并比较各竞品的核心功能特性"
    }},
    {{
      "task_name": "商业模式分析",
      "acceptance_criteria": "分析各竞品的盈利模式和商业策略"
    }},
    {{
      "task_name": "用户评价分析",
      "acceptance_criteria": "收集并分析用户对各竞品的评价和反馈"
    }}
  ]
}}
"""

# 原始规划提示词（作为备用）
PLANNING_PROMPT = """你是一个顶级的AI战略分析师。你的任务是理解用户的需求，并将其分解为一个详细、可执行的竞品分析计划。

用户的原始输入是: '{initial_input}'

**你的思维过程 (Chain of Thought):**
1.  **识别核心实体**: 从用户输入中提取出核心的参照产品或领域。
2.  **识别数量**: 确定用户期望分析的竞品数量。
3.  **识别竞品**: 基于核心实体，列出最相关、最重要的竞品名称。
4.  **设计分析框架**: 作为一个专业的分析师，你应该知道一份标准的竞品分析报告包含哪些维度。请设计一个通用的分析框架，至少包含4个关键分析维度（子任务）。
5.  **设定验收标准**: 为每一个分析维度设定一个明确、可量化的验收标准。这是确保后续工作质量的关键。

**输出格式:**
请严格按照下面的JSON格式输出你的计划。每个子任务必须包含task_name和acceptance_criteria两个字段。

例如：
{{
  "competitors_to_research": ["竞品A", "竞品B"],
  "sub_tasks": [
    {{
      "task_name": "市场占有率分析",
      "acceptance_criteria": "提供每个竞品的市场份额数据和趋势分析"
    }},
    {{
      "task_name": "功能对比分析",
      "acceptance_criteria": "列出并比较各竞品的核心功能特性"
    }}
  ]
}}
"""
# 创建两个规划链
planning_chain = (
    ChatPromptTemplate.from_template(PLANNING_PROMPT) | llm.with_structured_output(Plan)
)

enhanced_planning_chain = (
    ChatPromptTemplate.from_template(ENHANCED_PLANNING_PROMPT) | llm.with_structured_output(Plan)
)


# --- 2. 子任务执行 ---
SUBTASK_EXECUTION_PROMPT = """你是一个高效的信息搜集员。你的目标是完成一个具体的调研任务。

**当前分析的竞品**: '{competitor}'
**需要完成的子任务**: '{sub_task}'
**你需要寻找的信息类型**: '{criteria}'

**你的行动指令:**
1.  **生成搜索词**: 基于以上信息，生成2-3个最可能找到答案的Google搜索查询词。
2.  **执行搜索与抓取**: (此部分由外部工具完成)
3.  **信息提取**: 你将收到一堆从网页上抓取下来的原始文本。请仔细阅读，并根据子任务和信息要求，用一段话总结出最关键的结论。

**原始文本资料:**
---
{scraped_content}
---

**你的结论 (一段总结性文字):**
"""
subtask_execution_chain = ChatPromptTemplate.from_template(SUBTASK_EXECUTION_PROMPT) | llm


# --- 3. 反思与验证 ---
class ValidationResult(BaseModel):
    is_sufficient: bool = Field(description="判断结论是否满足验收标准")
    reasoning: str = Field(description="给出判断的理由。如果不足，请明确指出缺少了哪些信息。")

VALIDATION_PROMPT = """你是一个严格的质量控制员。你的职责是评估一份调研结论是否满足既定的质量标准。

**调研结论:**
'{conclusion}'

**验收标准:**
'{criteria}'

**你的任务:**
请判断上面的“调研结论”是否完全满足“验收标准”。
- 如果满足，请回答 `is_sufficient: true`。
- 如果不满足，请回答 `is_sufficient: false`，并在 `reasoning` 中清晰地说明为什么不满足，缺少了什么。
"""
validation_chain = (
    ChatPromptTemplate.from_template(VALIDATION_PROMPT) | llm.with_structured_output(ValidationResult)
)


# --- 4. 最终报告生成 ---
FINAL_REPORT_PROMPT = """你是一位首席战略官，正在撰写一份最终的竞品分析报告。
下面是已经通过质量验证的所有竞品的深度分析材料。

**分析材料:**
{analysis_summary}

请基于这些材料，撰写一份完整、逻辑清晰、富有洞察力的专业报告（Markdown格式）。
报告应包含执行摘要、对每个竞品的独立分析章节，以及最终的横向对比和战略建议。
"""
final_report_chain = ChatPromptTemplate.from_template(FINAL_REPORT_PROMPT) | llm