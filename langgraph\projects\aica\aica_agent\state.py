# aica_agent/state.py  状态是 Agent 的记忆

from typing import List, Dict, Optional, Any

from dataclasses import dataclass
from pydantic import BaseModel, Field

class SubTask(BaseModel):
    """一个具体的、可执行的子任务"""
    task_name: str = Field(description="子任务的名称, 例如 '核心功能与产品定位'")
    acceptance_criteria: str = Field(description="用于验证该子任务是否完成的验收标准")
    result: Optional[str] = Field(default=None, description="该子任务执行后的结论")
    is_complete: bool = Field(default=False)

class CompetitorAnalysis(BaseModel):
    """对单个竞品的完整分析，包含多个子任务"""
    competitor_name: str
    sub_tasks: List[SubTask]

class AgentState(BaseModel):
    """定义整个图的状态"""
    # --- 初始输入 ---
    initial_input: str
    
    # --- 规划阶段 ---
    analysis_plan: List[CompetitorAnalysis] = Field(default_factory=list, description="完整的分析计划，包含所有竞品及其子任务")
    
    # --- 执行控制 ---
    # 使用 Any 来存储迭代器，因为 Pydantic 无法直接序列化它
    competitor_iterator: Any = Field(default=None, exclude=True, description="用于遍历竞品的迭代器")
    sub_task_iterator: Any = Field(default=None, exclude=True, description="用于遍历子任务的迭代器")
    
    current_competitor_name: Optional[str] = None
    current_sub_task_name: Optional[str] = None
    current_sub_task_criteria: Optional[str] = None
    current_retry_count: int = 0
    
    # --- 执行产物 ---
    current_sub_task_result: Optional[str] = None # 子任务执行的临时结果
    final_report_markdown: str = ""
    report_filepath: str = ""