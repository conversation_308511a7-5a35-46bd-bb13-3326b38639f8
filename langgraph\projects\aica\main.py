import os
from aica_agent.graph import build_graph
from config import REPORTS_DIR
from aica_agent.utils.logger import init_logger, get_logger

def main():
    """主程序入口"""
    # 初始化日志系统
    logger = init_logger("logs")

    # 确保报告输出目录存在
    os.makedirs(REPORTS_DIR, exist_ok=True)
    os.makedirs("docs", exist_ok=True)

    # 编译LangGraph应用
    app = build_graph()

    # 从终端获取用户输入
    topic = input("请输入您想分析的产品、公司或领域：")
    logger.info(f"用户输入: {topic}")

    # 定义初始状态
    initial_state = {"initial_input": topic}

    logger.info("开始执行竞品分析 Agent")
    logger.info("=" * 80)

    # 流式执行图，并打印每一步的状态变化
    for s in app.stream(initial_state, {"recursion_limit": 150}):
        # s 是一个字典，key是刚刚执行完的节点名
        node_name = list(s.keys())[0]
        node_output = s[node_name]

        logger.info(f"节点 '{node_name}' 执行完毕")
        logger.debug(f"节点输出: {node_output}")

    logger.info("=" * 80)
    logger.info("Agent 执行完成")

if __name__ == "__main__":
    main()