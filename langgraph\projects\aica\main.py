import os
from aica_agent.graph import build_graph
from config import REPORTS_DIR

def main():
    """主程序入口"""
    # 确保报告输出目录存在
    os.makedirs(REPORTS_DIR, exist_ok=True)
    
    # 编译LangGraph应用
    app = build_graph()
    
    # 从终端获取用户输入
    topic = input("请输入您想分析的产品、公司或领域：")
    
    # 定义初始状态
    initial_state = {"initial_input": topic}
    
    print("\n--- 开始执行竞品分析 Agent ---\n")
    # 流式执行图，并打印每一步的状态变化
    for s in app.stream(initial_state, {"recursion_limit": 150}):
        # s 是一个字典，key是刚刚执行完的节点名
        node_name = list(s.keys())[0]
        print(f"--- 节点 '{node_name}' 执行完毕 ---")
        # 可以取消下面的注释来查看每一步的完整状态
        print(s[node_name]) 
        print("\n")

    print("--- Agent 执行完成 ---")

if __name__ == "__main__":
    main()