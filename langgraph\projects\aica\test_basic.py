#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AICA 系统基础测试脚本
测试不依赖网络的核心功能
"""

import os
import sys
from datetime import datetime

def test_basic_imports():
    """测试基础模块导入"""
    print("🔍 测试基础模块导入...")
    
    try:
        from aica_agent.utils.logger import AICALogger, get_logger, init_logger
        print("✅ 日志模块导入成功")
    except Exception as e:
        print(f"❌ 日志模块导入失败: {e}")
        return False
    
    try:
        from aica_agent.state import AgentState, CompetitorAnalysis, SubTask
        print("✅ 状态模块导入成功")
    except Exception as e:
        print(f"❌ 状态模块导入失败: {e}")
        return False
    
    try:
        from config import MAX_SUBTASK_RETRIES, REPORTS_DIR
        print("✅ 配置模块导入成功")
    except Exception as e:
        print(f"❌ 配置模块导入失败: {e}")
        return False
    
    return True

def test_logger_functionality():
    """测试日志系统功能"""
    print("\n🔍 测试日志系统功能...")
    
    try:
        from aica_agent.utils.logger import init_logger
        
        # 初始化日志器
        logger = init_logger("test_logs")
        
        # 测试不同类型的日志
        logger.info("这是一条信息日志")
        logger.planning("这是规划阶段日志")
        logger.execution("这是执行阶段日志")
        logger.search("这是搜索阶段日志")
        logger.scrape("这是抓取阶段日志")
        logger.reflection("这是反思阶段日志")
        logger.result("这是结果阶段日志")
        logger.warning("这是警告日志")
        logger.error("这是错误日志")
        
        # 测试输入输出日志
        logger.log_input_output("TEST", {"input": "test"}, {"output": "success"})
        
        print("✅ 日志系统功能测试成功")
        return True
    except Exception as e:
        print(f"❌ 日志系统功能测试失败: {e}")
        return False

def test_state_management():
    """测试状态管理"""
    print("\n🔍 测试状态管理...")
    
    try:
        from aica_agent.state import AgentState, CompetitorAnalysis, SubTask
        
        # 创建子任务
        subtask = SubTask(
            task_name="测试任务",
            acceptance_criteria="测试标准",
            result="测试结果",
            is_complete=True
        )
        
        # 创建竞品分析
        competitor = CompetitorAnalysis(
            competitor_name="测试竞品",
            sub_tasks=[subtask]
        )
        
        # 创建智能体状态
        state = AgentState(
            initial_input="测试输入",
            analysis_plan=[competitor],
            current_competitor_name="测试竞品",
            current_sub_task_name="测试任务",
            current_sub_task_criteria="测试标准",
            current_retry_count=0,
            current_sub_task_result="测试结果",
            final_report_markdown="# 测试报告",
            report_filepath="test.md"
        )
        
        print(f"✅ 状态管理测试成功，状态包含 {len(state.analysis_plan)} 个竞品分析")
        return True
    except Exception as e:
        print(f"❌ 状态管理测试失败: {e}")
        return False

def test_directory_structure():
    """测试目录结构"""
    print("\n🔍 测试目录结构...")
    
    required_dirs = ["docs", "logs", "aica_agent", "aica_agent/tools", "aica_agent/utils"]
    
    for dir_name in required_dirs:
        if os.path.exists(dir_name):
            print(f"✅ 目录 {dir_name} 存在")
        else:
            print(f"❌ 目录 {dir_name} 不存在")
            return False
    
    return True

def test_file_operations():
    """测试文件操作"""
    print("\n🔍 测试文件操作...")
    
    try:
        # 测试创建日志文件
        from aica_agent.utils.logger import init_logger
        logger = init_logger("test_logs")
        
        # 测试创建文档文件
        test_content = "# 测试文档\n\n这是一个测试文档。"
        test_file = "docs/test_document.md"
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        # 验证文件是否创建成功
        if os.path.exists(test_file):
            print(f"✅ 文件操作测试成功，创建了 {test_file}")
            
            # 清理测试文件
            os.remove(test_file)
            return True
        else:
            print("❌ 文件操作测试失败：文件未创建")
            return False
    except Exception as e:
        print(f"❌ 文件操作测试失败: {e}")
        return False

def test_llm_basic():
    """测试LLM基础连接"""
    print("\n🔍 测试LLM基础连接...")
    
    try:
        from aica_agent.chains import get_ollama_llm
        
        llm = get_ollama_llm()
        if llm is not None:
            print("✅ LLM实例创建成功")
            
            # 尝试简单调用
            try:
                response = llm.invoke("你好")
                print(f"✅ LLM调用成功，响应长度: {len(response.content)} 字符")
                return True
            except Exception as e:
                print(f"⚠️ LLM调用失败，但实例创建成功: {e}")
                return True  # 实例创建成功就算通过
        else:
            print("❌ LLM实例创建失败")
            return False
    except Exception as e:
        print(f"❌ LLM基础连接测试失败: {e}")
        return False

def test_config_loading():
    """测试配置加载"""
    print("\n🔍 测试配置加载...")
    
    try:
        import config
        
        # 检查关键配置项
        required_configs = [
            'MAX_SUBTASK_RETRIES',
            'REPORTS_DIR',
            'REPORT_FILENAME'
        ]
        
        for config_name in required_configs:
            if hasattr(config, config_name):
                value = getattr(config, config_name)
                print(f"✅ 配置项 {config_name} = {value}")
            else:
                print(f"❌ 缺少配置项 {config_name}")
                return False
        
        return True
    except Exception as e:
        print(f"❌ 配置加载测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 AICA 系统基础测试开始")
    print("=" * 60)
    
    test_results = []
    
    # 运行所有基础测试
    test_results.append(("基础模块导入", test_basic_imports()))
    test_results.append(("目录结构", test_directory_structure()))
    test_results.append(("配置加载", test_config_loading()))
    test_results.append(("日志系统功能", test_logger_functionality()))
    test_results.append(("状态管理", test_state_management()))
    test_results.append(("文件操作", test_file_operations()))
    test_results.append(("LLM基础连接", test_llm_basic()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 基础测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 个基础测试通过")
    
    if passed == total:
        print("🎉 所有基础测试通过！核心功能正常。")
        print("\n📝 下一步:")
        print("1. 安装网络依赖: pip install ddgs langgraph langchain-community")
        print("2. 配置LLM服务 (Ollama 或 OpenAI)")
        print("3. 运行完整测试: python test_system.py")
        print("4. 启动主程序: python main.py")
        return True
    else:
        print("⚠️  部分基础测试失败，请检查系统配置。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
