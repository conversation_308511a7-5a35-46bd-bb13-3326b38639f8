# AICA - AI Competitive Analysis System

🤖 基于 LangGraph 的智能竞品分析系统

## 🌟 特性

- ✅ **智能规划**: 自动识别竞品并制定分析计划
- ✅ **网络搜索**: 集成 DuckDuckGo 搜索引擎
- ✅ **内容抓取**: 自动抓取和分析网页内容
- ✅ **质量验证**: 内置反思机制确保结果质量
- ✅ **彩色日志**: 完整的彩色日志系统，便于调试
- ✅ **Markdown 报告**: 生成结构化的分析报告
- ✅ **模块化设计**: 易于扩展和维护

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- 稳定的网络连接
- 可用的 LLM 服务（Ollama 或 OpenAI）

### 2. 安装依赖

```bash
pip install langgraph langchain langchain-community langchain-ollama ddgs colorama pydantic
```

### 3. 配置 LLM

#### 使用 Ollama（推荐）

```bash
# 安装 Ollama
# 下载并运行模型
ollama pull qwen2.5:32b
ollama serve
```

#### 使用 OpenAI

创建 `.env` 文件：
```
OPENAI_API_KEY=your_api_key_here
```

### 4. 运行系统

```bash
# 测试系统
python test_system.py

# 运行主程序
python main.py
```

## 📁 项目结构

```
aica/
├── aica_agent/           # 核心智能体模块
│   ├── chains.py        # LLM 链定义
│   ├── graph.py         # 工作流定义
│   ├── nodes.py         # 节点实现
│   ├── state.py         # 状态管理
│   ├── tools/           # 工具模块
│   └── utils/           # 工具类
├── docs/                # 文档和报告输出
├── logs/                # 日志文件
├── config.py            # 配置文件
├── main.py              # 主程序
└── test_system.py       # 测试脚本
```

## 🎯 使用示例

### 输入示例
```
请输入您想分析的产品、公司或领域：微信小程序
```

### 系统会自动：
1. 🔍 搜索相关竞品信息
2. 📋 制定详细分析计划
3. 🌐 收集网络数据
4. 🧠 AI 分析处理
5. ✅ 质量验证
6. 📄 生成最终报告

### 输出文件
- **报告**: `docs/competitive_analysis_report_YYYYMMDD_HHMMSS.md`
- **日志**: `logs/aica_run_YYYYMMDD_HHMMSS.log`

## 🎨 日志系统

系统提供彩色日志输出，便于监控和调试：

- 🔵 **PLANNING** - 规划阶段
- 🟣 **EXECUTION** - 执行阶段  
- 🟡 **REFLECTION** - 反思阶段
- 🔷 **SEARCH** - 搜索阶段
- ⚪ **SCRAPE** - 抓取阶段
- 🟢 **RESULT** - 结果阶段

## ⚙️ 配置说明

### config.py 主要参数

```python
# LLM 配置
LLM_MODEL = "gpt-4o"                    # OpenAI 模型
OLLAMA_BASE_URL = "http://192.168.0.92:8080"  # Ollama 服务地址
OLLAMA_MODEL = "qwen2.5:32b"            # Ollama 模型

# 系统参数
MAX_COMPETITORS = 5                      # 最大竞品数量
MAX_SUBTASK_RETRIES = 3                 # 子任务最大重试次数

# 文件路径
REPORTS_DIR = "reports"                  # 报告目录（已弃用）
```

## 🔧 故障排除

### 常见问题

1. **搜索失败**
   - 检查网络连接
   - 确认 DuckDuckGo 可访问

2. **LLM 连接失败**
   - 检查 Ollama 服务状态：`ollama list`
   - 验证 OpenAI API 密钥

3. **编码错误**
   - 确保终端支持 UTF-8
   - 检查系统区域设置

4. **权限错误**
   - 检查 docs/ 和 logs/ 目录写入权限

### 调试方法

1. 运行测试脚本：`python test_system.py`
2. 查看彩色日志输出
3. 检查日志文件详细信息
4. 使用 DEBUG 级别日志

## 📊 工作流程

```mermaid
graph TD
    A[用户输入] --> B[网络搜索]
    B --> C[规划分析]
    C --> D[选择竞品]
    D --> E[执行子任务]
    E --> F[搜索信息]
    F --> G[抓取内容]
    G --> H[AI分析]
    H --> I[质量验证]
    I --> J{验证通过?}
    J -->|否| E
    J -->|是| K{还有任务?}
    K -->|是| E
    K -->|否| L{还有竞品?}
    L -->|是| D
    L -->|否| M[生成报告]
```

## 🔄 版本历史

### v2.0 (2025-08-16)
- ✨ 增强规划阶段网络搜索功能
- 🎨 完整的彩色日志系统
- 📄 改用 Markdown 报告输出
- 🏗️ 优化项目结构

### v1.0
- 🎯 基础竞品分析功能
- 📊 PDF 报告生成
- 🔄 简单重试机制

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 📞 支持

如有问题，请查看：
1. 📖 [项目分析文档](docs/project_analysis.md)
2. 🧪 运行测试脚本
3. 📝 查看日志文件

---

*由 AICA 智能竞品分析系统提供支持* 🚀
